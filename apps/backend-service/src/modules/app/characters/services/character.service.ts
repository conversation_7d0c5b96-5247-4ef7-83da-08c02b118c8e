import { getUserBySupabaseId } from '@/modules/app/user/repositories/user.repository';
import type { Character } from '@/lib/db/schema'
import type { Env } from '@/types/env'
import type { CharacterCreateData, CharacterUpdateData, CharacterQueryParams, CharacterStats } from '../types/character'
import { CharacterRepository } from '../repositories'
import { PermissionService } from './permission.service'

export class CharacterService {
  private characterRepo: CharacterRepository
  private permissionService: PermissionService

  constructor() {
    this.characterRepo = new CharacterRepository()
    this.permissionService = new PermissionService()
  }

  /**
   * 创建新角色
   */
  async createCharacter(env: Env, supabaseUserId: string, data: Omit<CharacterCreateData, 'userId'>): Promise<Character> {
    // 获取数据库用户信息
    const dbUser = await getUserBySupabaseId(env, supabaseUserId)
    if (!dbUser) {
      throw new Error('用户不存在')
    }

    // 检查用户角色创建限制
    const canCreateResult = await this.permissionService.checkCanCreateCharacter(env, dbUser.id)
    if (!canCreateResult.canCreate) {
      throw new Error(canCreateResult.reason || '无法创建更多角色')
    }

    // 创建角色数据
    const createData: CharacterCreateData = {
      userId: dbUser.id,
      ...data
    }

    return await this.characterRepo.create(env, createData)
  }

  /**
   * 获取用户角色列表
   */
  async getUserCharacters(env: Env, supabaseUserId: string): Promise<Character[]> {
    // 获取数据库用户信息
    const dbUser = await getUserBySupabaseId(env, supabaseUserId)
    if (!dbUser) {
      throw new Error('用户不存在')
    }

    return await this.characterRepo.findByUserId(env, dbUser.id)
  }

  /**
   * 获取角色详情
   */
  async getCharacterById(env: Env, id: string, supabaseUserId?: string): Promise<Character> {
    const character = await this.characterRepo.findById(env, id)
    if (!character) {
      throw new Error('角色不存在')
    }

    // 如果是私有角色，需要验证权限
    if (!character.isPublic && supabaseUserId) {
      const dbUser = await getUserBySupabaseId(env, supabaseUserId)
      if (!dbUser || character.userId !== dbUser.id) {
        throw new Error('无权限访问该角色')
      }
    } else if (!character.isPublic && !supabaseUserId) {
      throw new Error('私有角色需要登录访问')
    }

    return character
  }

  /**
   * 更新角色信息
   */
  async updateCharacter(env: Env, id: string, supabaseUserId: string, data: CharacterUpdateData): Promise<Character> {
    // 检查权限
    const canUpdate = await this.permissionService.checkCanUpdateCharacter(env, id, supabaseUserId)
    if (!canUpdate) {
      throw new Error('无权限修改该角色')
    }

    return await this.characterRepo.update(env, id, data)
  }

  /**
   * 删除角色
   */
  async deleteCharacter(env: Env, id: string, supabaseUserId: string): Promise<void> {
    // 检查权限
    const canDelete = await this.permissionService.checkCanDeleteCharacter(env, id, supabaseUserId)
    if (!canDelete) {
      throw new Error('无权限删除该角色')
    }

    await this.characterRepo.delete(env, id)
  }

  /**
   * 获取公开角色列表
   */
  async getPublicCharacters(env: Env, params: CharacterQueryParams): Promise<Character[]> {
    return await this.characterRepo.findPublic(env, params)
  }

  /**
   * 获取系统角色列表
   */
  async getSystemCharacters(env: Env, params: CharacterQueryParams): Promise<Character[]> {
    return await this.characterRepo.findSystem(env, params)
  }

  /**
   * 获取用户角色统计信息
   */
  async getUserCharacterStats(env: Env, supabaseUserId: string): Promise<CharacterStats> {
    // 获取数据库用户信息
    const dbUser = await getUserBySupabaseId(env, supabaseUserId)
    if (!dbUser) {
      throw new Error('用户不存在')
    }

    return await this.permissionService.getUserCharacterStats(env, dbUser.id)
  }

  /**
   * 获取角色信息（包含系统角色标识）
   */
  async getRoleInfoById(env: Env, roleId: string): Promise<(Character & { isSystemRole: boolean }) | null> {
    return await this.characterRepo.findRoleInfoById(env, roleId)
  }
}