import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import { authMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'
import { getChat<PERSON>and<PERSON>, create<PERSON>hatHand<PERSON>, update<PERSON>hat<PERSON>and<PERSON>, deleteChatHandler } from '../controllers/chat.controller'
import { createChatSchema, updateChatSchema } from '@/lib/chat/handlers'

// 导入新的控制器
import {
  generateChatBackgroundHandler,
  getChatBackgroundHandler
} from '../controllers/background.controller'
import {
  getDialogueHandler,
  getDialogueListHandler
} from '../controllers/dialogue.controller'
import {
  sseEventsHandler,
  sseTriggerHandler,
  sseStatsHandler
} from '../controllers/sse.controller'

// 定义验证模式
const backgroundGenerationSchema = z.object({
  sceneDescription: z.string().min(1).max(500)
})

const sseEventSchema = z.object({
  event: z.string().optional(),
  data: z.any().optional(),
  targetUserId: z.string().optional()
})

const route = new Hono()
route.use(authMiddleware, languageMiddleware)

// 原有聊天路由
route.get('/:id', getChatHandler)
route.post('/', zValidator('json', createChatSchema), createChatHandler)
route.put('/:id', zValidator('json', updateChatSchema), updateChatHandler)
route.delete('/:id', deleteChatHandler)

// 背景图生成路由
route.post('/background/generate/:chatId', zValidator('json', backgroundGenerationSchema), generateChatBackgroundHandler)
route.get('/background/:chatId', getChatBackgroundHandler)

// 剧本对话路由
route.get('/dialogue', getDialogueHandler)
route.get('/dialogue/list', getDialogueListHandler)

// SSE实时通信路由
route.get('/sse/events', sseEventsHandler)
route.post('/sse/trigger', zValidator('json', sseEventSchema), sseTriggerHandler)
route.get('/sse/stats', sseStatsHandler)

export default route