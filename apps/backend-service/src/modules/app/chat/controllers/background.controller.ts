/**
 * 聊天背景图控制器
 * 处理聊天背景图生成和获取相关的HTTP请求
 */

import type { Context } from 'hono'
import type { Env } from '@/types/env'
import type { SupportedLanguage } from '@/i18n/config'
import { BackgroundImageService } from '@/lib/image-generation/background-service'
import { getUserBySupabaseId } from '@/modules/app/user/repositories/user.repository'

// 定义应用上下文类型
type AppContext = {
  Bindings: Env
  Variables: {
    language: SupportedLanguage
    t: (key: string, params?: Record<string, string | number>) => string
    user?: any
  }
}

// 根据Supabase用户ID获取数据库用户ID
async function getDbUserId(env: Env, supabaseUserId: string): Promise<string | null> {
  const dbUser = await getUserBySupabaseId(env, supabaseUserId)
  return dbUser?.id || null
}

// ==================== 背景图控制器 ====================

/**
 * 为聊天生成背景图
 */
export async function generateChatBackgroundHandler(c: Context<AppContext>): Promise<Response> {
  try {
    const chatId = c.req.param('chatId')
    const supabaseUser = c.get('user')
    const t = c.get('t')

    if (!supabaseUser) {
      return c.json({ error: t('auth_failed') }, 401)
    }

    // 获取数据库用户ID
    const userId = await getDbUserId(c.env, supabaseUser.id)
    if (!userId) {
      return c.json({ error: t('user_not_found') }, 404)
    }

    const requestData = await c.req.json()
    const { sceneDescription } = requestData

    if (!sceneDescription || typeof sceneDescription !== 'string') {
      return c.json({ error: t('scene_description_required') }, 400)
    }

    if (sceneDescription.length > 500) {
      return c.json({ error: t('scene_description_too_long') }, 400)
    }

    const backgroundService = new BackgroundImageService(c.env)
    const backgroundImageUrl = await backgroundService.generateChatBackground(
      chatId,
      sceneDescription,
      userId
    )

    return c.json({
      success: true,
      message: t('generate_success'),
      data: {
        backgroundImageUrl,
        chatId,
      },
    })
  } catch (error) {
    const t = c.get('t')
    console.error('生成背景图失败:', error)

    if (error instanceof Error) {
      if (error.message.includes('不存在') || error.message.includes('无权限')) {
        return c.json({ error: error.message }, 403)
      }
    }

    return c.json({ error: t('generate_failed') }, 500)
  }
}

/**
 * 获取聊天背景图
 */
export async function getChatBackgroundHandler(c: Context<AppContext>): Promise<Response> {
  try {
    const chatId = c.req.param('chatId')
    const supabaseUser = c.get('user')
    const t = c.get('t')

    if (!supabaseUser) {
      return c.json({ error: t('auth_failed') }, 401)
    }

    // 获取数据库用户ID
    const userId = await getDbUserId(c.env, supabaseUser.id)
    if (!userId) {
      return c.json({ error: t('user_not_found') }, 404)
    }

    const backgroundService = new BackgroundImageService(c.env)
    const backgroundInfo = await backgroundService.getChatBackground(chatId, userId)

    return c.json({
      success: true,
      message: t('get_success'),
      data: {
        backgroundImageUrl: backgroundInfo?.backgroundImageUrl || null,
        backgroundSceneDescription: backgroundInfo?.backgroundSceneDescription || null,
        chatId,
      },
    })
  } catch (error) {
    const t = c.get('t')
    console.error('获取背景图失败:', error)

    if (error instanceof Error) {
      if (error.message.includes('不存在') || error.message.includes('无权限')) {
        return c.json({ error: error.message }, 403)
      }
    }

    return c.json({ error: t('get_background_failed') }, 500)
  }
}
