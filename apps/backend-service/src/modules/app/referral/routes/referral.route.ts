import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import { authMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'

// 导入控制器
import {
  generateInviteCodeHandler,
  getMyInviteCodeHandler,
  validateInviteCodeHandler,
  getMyInviteesHandler,
  getCommissionAccountHandler,
  getCommissionRecordsHandler,
  createWithdrawRequestHandler,
  getWithdrawRequestsHandler
} from '../controllers/referral.controller'

// ==================== 验证模式 ====================

// 验证邀请码模式
const validateInviteCodeSchema = z.object({
  code: z.string().min(1)
})

// 提现申请模式
const withdrawRequestSchema = z.object({
  amount: z.number().min(100, '最低提现金额为100元'),
  bankInfo: z.object({
    bankName: z.string().min(1, '银行名称不能为空'),
    accountNumber: z.string().min(1, '银行账号不能为空'),
    accountName: z.string().min(1, '账户姓名不能为空'),
    branchName: z.string().optional()
  }),
  remark: z.string().optional()
})

// 分页查询模式
const paginationSchema = z.object({
  limit: z.string().optional().default('20'),
  offset: z.string().optional().default('0')
})

const route = new Hono()

// 应用中间件
route.use(authMiddleware, languageMiddleware)

// ==================== 邀请码管理路由 ====================

/**
 * 生成邀请码
 * POST /api/app/referral/invite-code/generate
 */
route.post('/invite-code/generate', generateInviteCodeHandler)

/**
 * 获取我的邀请码信息
 * GET /api/app/referral/invite-code/my
 */
route.get('/invite-code/my', getMyInviteCodeHandler)

/**
 * 验证邀请码
 * POST /api/app/referral/invite-code/validate
 */
route.post('/invite-code/validate', zValidator('json', validateInviteCodeSchema), validateInviteCodeHandler)

/**
 * 获取我的邀请列表
 * GET /api/app/referral/invitees
 */
route.get('/invitees', zValidator('query', paginationSchema), getMyInviteesHandler)

// ==================== 佣金账户管理路由 ====================

/**
 * 获取佣金账户信息
 * GET /api/app/referral/commission/account
 */
route.get('/commission/account', getCommissionAccountHandler)

/**
 * 获取佣金记录
 * GET /api/app/referral/commission/records
 */
route.get('/commission/records', zValidator('query', paginationSchema), getCommissionRecordsHandler)

/**
 * 创建提现申请
 * POST /api/app/referral/commission/withdraw
 */
route.post('/commission/withdraw', zValidator('json', withdrawRequestSchema), createWithdrawRequestHandler)

/**
 * 获取提现记录
 * GET /api/app/referral/commission/withdraws
 */
route.get('/commission/withdraws', zValidator('query', paginationSchema), getWithdrawRequestsHandler)

export default route
