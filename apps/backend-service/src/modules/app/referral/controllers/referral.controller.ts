import { Context } from 'hono'
import type { Env } from '@/types/env'
import type { SupportedLanguage } from '@/i18n/config'
import { getUserBySupabaseId } from '@/modules/app/user/repositories/user.repository'
import {
  createInviteCode,
  getUserInviteCode,
  validateInviteCode,
  getUserInvitees,
  getInviteStats
} from '@/lib/db/queries/referral'
import {
  getUserCommissionAccount,
  getUserCommissionRecords,
  getUserCommissionStats,
  createWithdrawRequest,
  getUserWithdrawRequests,
  freezeCommissionBalance
} from '@/lib/db/queries/commission'
import { getWithdrawConfig, calculateWithdrawFee } from '@/lib/db/queries/system-config'

// 辅助函数：从Supabase用户上下文解析本地用户ID
async function resolveLocalUserId(c: Context<{ Bindings: Env }>): Promise<string> {
  const supabaseUser = c.get('user')
  if (!supabaseUser) {
    throw new Error('用户未认证')
  }

  const dbUser = await getUserBySupabaseId(c.env, supabaseUser.id)
  if (!dbUser) {
    throw new Error('用户数据不存在')
  }

  return dbUser.id
}

// 获取国际化函数
function getI18n(c: Context<{ Bindings: Env }>) {
  const language = (c.get('language') || 'zh') as SupportedLanguage
  return (key: string) => {
    // 简化的国际化实现
    const messages: Record<string, Record<string, string>> = {
      zh: {
        invite_code_generate_failed: '生成邀请码失败',
        invite_code_info_get_failed: '获取邀请码信息失败',
        invite_code_validate_failed: '验证邀请码失败',
        invitees_get_failed: '获取邀请列表失败',
        commission_account_get_failed: '获取佣金账户失败',
        commission_records_get_failed: '获取佣金记录失败',
        withdraw_request_failed: '提现申请失败',
        withdraw_requests_get_failed: '获取提现记录失败'
      },
      en: {
        invite_code_generate_failed: 'Failed to generate invite code',
        invite_code_info_get_failed: 'Failed to get invite code info',
        invite_code_validate_failed: 'Failed to validate invite code',
        invitees_get_failed: 'Failed to get invitees',
        commission_account_get_failed: 'Failed to get commission account',
        commission_records_get_failed: 'Failed to get commission records',
        withdraw_request_failed: 'Failed to create withdraw request',
        withdraw_requests_get_failed: 'Failed to get withdraw requests'
      }
    }
    return messages[language]?.[key] || key
  }
}

// ==================== 邀请码管理 ====================

/**
 * 生成邀请码处理器
 * POST /api/app/referral/invite-code/generate
 */
export const generateInviteCodeHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const userId = await resolveLocalUserId(c)
    const inviteCode = await createInviteCode(c.env, userId)

    return c.json({
      success: true,
      data: inviteCode
    })
  } catch (error) {
    console.error('Generate invite code error:', error)
    const t = getI18n(c)
    return c.json(
      {
        success: false,
        error: error instanceof Error ? error.message : t('invite_code_generate_failed')
      },
      500
    )
  }
}

/**
 * 获取我的邀请码信息处理器
 * GET /api/app/referral/invite-code/my
 */
export const getMyInviteCodeHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const userId = await resolveLocalUserId(c)
    const myInviteCode = await getUserInviteCode(c.env, userId)
    const stats = await getInviteStats(c.env, userId)
    const commissionAccount = await getUserCommissionAccount(c.env, userId)

    return c.json({
      success: true,
      data: {
        inviteCode: myInviteCode,
        stats,
        commissionAccount
      }
    })
  } catch (error) {
    console.error('Get my invite code error:', error)
    const t = getI18n(c)
    return c.json(
      {
        success: false,
        error: t('invite_code_info_get_failed')
      },
      500
    )
  }
}

/**
 * 验证邀请码处理器
 * POST /api/app/referral/invite-code/validate
 */
export const validateInviteCodeHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const { code } = await c.req.json()
    
    if (!code || typeof code !== 'string') {
      return c.json(
        {
          success: false,
          error: '邀请码不能为空'
        },
        400
      )
    }

    const validation = await validateInviteCode(c.env, code)

    return c.json({
      success: true,
      data: validation
    })
  } catch (error) {
    console.error('Validate invite code error:', error)
    const t = getI18n(c)
    return c.json(
      {
        success: false,
        error: t('invite_code_validate_failed')
      },
      500
    )
  }
}

/**
 * 获取我的邀请列表处理器
 * GET /api/app/referral/invitees
 */
export const getMyInviteesHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const userId = await resolveLocalUserId(c)
    const query = c.req.query()
    const limit = parseInt(query.limit || '20')
    const offset = parseInt(query.offset || '0')

    const invitees = await getUserInvitees(c.env, userId, { limit, offset })

    return c.json({
      success: true,
      data: invitees,
      pagination: {
        limit,
        offset,
        total: invitees.length
      }
    })
  } catch (error) {
    console.error('Get invitees error:', error)
    const t = getI18n(c)
    return c.json(
      {
        success: false,
        error: t('invitees_get_failed')
      },
      500
    )
  }
}

// ==================== 佣金账户管理 ====================

/**
 * 获取佣金账户信息处理器
 * GET /api/app/referral/commission/account
 */
export const getCommissionAccountHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const userId = await resolveLocalUserId(c)
    const account = await getUserCommissionAccount(c.env, userId)
    const stats = await getUserCommissionStats(c.env, userId)

    return c.json({
      success: true,
      data: {
        account: account || {
          totalEarned: '0.00',
          availableBalance: '0.00',
          frozenBalance: '0.00',
          totalWithdrawn: '0.00'
        },
        stats
      }
    })
  } catch (error) {
    console.error('Get commission account error:', error)
    const t = getI18n(c)
    return c.json(
      {
        success: false,
        error: t('commission_account_get_failed')
      },
      500
    )
  }
}

/**
 * 获取佣金记录处理器
 * GET /api/app/referral/commission/records
 */
export const getCommissionRecordsHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const userId = await resolveLocalUserId(c)
    const query = c.req.query()
    const limit = parseInt(query.limit || '20')
    const offset = parseInt(query.offset || '0')

    const records = await getUserCommissionRecords(c.env, userId, { limit, offset })

    return c.json({
      success: true,
      data: records,
      pagination: {
        limit,
        offset,
        total: records.length
      }
    })
  } catch (error) {
    console.error('Get commission records error:', error)
    const t = getI18n(c)
    return c.json(
      {
        success: false,
        error: t('commission_records_get_failed')
      },
      500
    )
  }
}

/**
 * 创建提现申请处理器
 * POST /api/app/referral/commission/withdraw
 */
export const createWithdrawRequestHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const userId = await resolveLocalUserId(c)
    const withdrawData = await c.req.json()

    // 获取提现配置
    const config = await getWithdrawConfig(c.env)
    const minAmount = parseFloat(config.minWithdrawAmount)

    if (withdrawData.amount < minAmount) {
      return c.json(
        {
          success: false,
          error: `最低提现金额为 ${minAmount} 元`
        },
        400
      )
    }

    // 计算手续费
    const fee = calculateWithdrawFee(withdrawData.amount, config.withdrawFeeRate)
    const actualAmount = withdrawData.amount - fee

    const withdrawRequest = await createWithdrawRequest(c.env, {
      userId,
      amount: withdrawData.amount,
      actualAmount,
      fee,
      bankInfo: withdrawData.bankInfo,
      remark: withdrawData.remark
    })

    return c.json({
      success: true,
      data: withdrawRequest,
      message: '提现申请提交成功，请等待审核'
    })
  } catch (error) {
    console.error('Create withdraw request error:', error)
    const t = getI18n(c)
    return c.json(
      {
        success: false,
        error: error instanceof Error ? error.message : t('withdraw_request_failed')
      },
      500
    )
  }
}

/**
 * 获取提现记录处理器
 * GET /api/app/referral/commission/withdraws
 */
export const getWithdrawRequestsHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const userId = await resolveLocalUserId(c)
    const query = c.req.query()
    const limit = parseInt(query.limit || '20')
    const offset = parseInt(query.offset || '0')

    const requests = await getUserWithdrawRequests(c.env, userId, { limit, offset })

    return c.json({
      success: true,
      data: requests,
      pagination: {
        limit,
        offset,
        total: requests.length
      }
    })
  } catch (error) {
    console.error('Get withdraw requests error:', error)
    const t = getI18n(c)
    return c.json(
      {
        success: false,
        error: t('withdraw_requests_get_failed')
      },
      500
    )
  }
}
