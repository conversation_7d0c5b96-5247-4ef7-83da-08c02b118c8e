/**
 * 积分模块路由
 * 统一管理积分商城和积分周期相关路由
 */

import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import { authMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'
import type { Env } from '@/types/env'
import type { SupportedLanguage } from '@/i18n/config'

// 积分商城控制器
import {
  getPointsPackagesHandler,
  getPointsPackageDetailHandler,
  createPointsPurchaseOrderHandler
} from '../controllers/points-store.controller'

// 积分周期控制器
import {
  getPointsCycleStatusHandler,
  initializePointsCycleHandler,
  checkAndResetCycleHandler,
  handleMembershipUpgradeHandler,
  batchProcessExpiredPointsHandler,
  getPointsCycleHealthHandler
} from '../controllers/points-cycle.controller'

const route = new Hono<{
  Bindings: Env
  Variables: {
    language: SupportedLanguage
    t: (key: string, params?: Record<string, string | number>) => string
    user?: any
  }
}>()

// ==================== 验证模式 ====================

// 创建积分包购买订单验证模式
const createPointsOrderSchema = z.object({
  description: z.string().optional(),
  paymentMethod: z.enum(['alipay', 'wechat']).default('alipay'),
  metadata: z.record(z.any()).optional()
})

// 初始化积分周期验证模式
const initializeCycleSchema = z.object({
  membershipLevel: z.enum(['FREE', 'PRO', 'ELITE', 'ULTRA']),
  startDate: z.string().datetime().optional(),
})

// 会员升级验证模式
const upgradeSchema = z.object({
  fromLevel: z.enum(['FREE', 'PRO', 'ELITE', 'ULTRA']),
  toLevel: z.enum(['FREE', 'PRO', 'ELITE', 'ULTRA']),
  remainingDays: z.number().min(0).max(30),
})

// ==================== 积分商城路由 ====================

/**
 * 获取积分包列表
 * GET /api/app/points/packages
 */
route.get('/packages', languageMiddleware, getPointsPackagesHandler)

/**
 * 获取单个积分包详情
 * GET /api/app/points/packages/:packageId
 */
route.get('/packages/:packageId', languageMiddleware, getPointsPackageDetailHandler)

/**
 * 创建积分包购买订单
 * POST /api/app/points/packages/:packageId/purchase
 */
route.post(
  '/packages/:packageId/purchase',
  authMiddleware,
  languageMiddleware,
  zValidator('json', createPointsOrderSchema),
  createPointsPurchaseOrderHandler
)

// ==================== 积分周期路由 ====================

/**
 * 获取用户积分周期状态
 * GET /api/app/points/cycle/status
 */
route.get('/cycle/status', authMiddleware, getPointsCycleStatusHandler)

/**
 * 初始化用户积分周期
 * POST /api/app/points/cycle/initialize
 */
route.post(
  '/cycle/initialize',
  authMiddleware,
  zValidator('json', initializeCycleSchema),
  initializePointsCycleHandler
)

/**
 * 手动检查并重置过期积分
 * POST /api/app/points/cycle/check-reset
 */
route.post('/cycle/check-reset', authMiddleware, checkAndResetCycleHandler)

/**
 * 处理会员升级积分补差
 * POST /api/app/points/cycle/upgrade
 */
route.post(
  '/cycle/upgrade',
  authMiddleware,
  zValidator('json', upgradeSchema),
  handleMembershipUpgradeHandler
)

/**
 * 批量处理过期积分（管理员接口）
 * POST /api/app/points/cycle/batch-process
 */
route.post('/cycle/batch-process', authMiddleware, batchProcessExpiredPointsHandler)

/**
 * 积分周期系统健康检查
 * GET /api/app/points/cycle/health
 */
route.get('/cycle/health', getPointsCycleHealthHandler)

// ==================== 通用健康检查路由 ====================

/**
 * 积分模块整体健康检查
 * GET /api/app/points/health
 */
route.get('/health', async (c) => {
  try {
    const healthData = {
      service: 'points-module',
      status: 'healthy',
      timestamp: new Date().toISOString(),
      components: {
        pointsStore: 'healthy',
        pointsCycle: 'healthy'
      },
      features: {
        packageManagement: true,
        orderCreation: true,
        cycleManagement: true,
        membershipUpgrade: true,
        batchProcessing: true
      }
    }

    return c.json({
      success: true,
      data: healthData
    })
  } catch (error) {
    console.error('积分模块健康检查失败:', error)
    return c.json(
      {
        success: false,
        error: '积分模块健康检查失败'
      },
      500
    )
  }
})

export default route
