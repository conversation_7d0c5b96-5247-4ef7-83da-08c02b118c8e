/**
 * 积分周期控制器
 * 处理积分周期管理相关的HTTP请求
 */

import type { Context } from 'hono'
import type { Env } from '@/types/env'
import { createPointsCycleManager } from '@/lib/membership/points-cycle'

// 定义应用上下文类型
type AppContext = {
  Bindings: Env
  Variables: {
    user?: any
  }
}

// ==================== 积分周期控制器 ====================

/**
 * 获取用户积分周期状态
 */
export async function getPointsCycleStatusHandler(c: Context<AppContext>): Promise<Response> {
  try {
    const user = c.get('user')
    const cycleManager = createPointsCycleManager(c.env)

    const cycleInfo = await cycleManager.getUserPointsCycle(user.id)

    return c.json({
      success: true,
      data: {
        userId: cycleInfo.userId,
        availablePoints: cycleInfo.availablePoints,
        cycleStartDate: cycleInfo.cycleStartDate,
        cycleEndDate: cycleInfo.cycleEndDate,
        membershipLevel: cycleInfo.membershipLevel,
        monthlyAllocation: cycleInfo.monthlyAllocation,
        cycleConsumed: cycleInfo.cycleConsumed,
        daysRemaining: cycleInfo.daysRemaining,
        isExpired: cycleInfo.isExpired,
        needsReset: cycleInfo.needsReset,
        progressPercentage:
          cycleInfo.monthlyAllocation > 0
            ? Math.round((cycleInfo.cycleConsumed / cycleInfo.monthlyAllocation) * 100)
            : 0,
      },
    })
  } catch (error) {
    console.error('获取积分周期状态失败:', error)
    return c.json(
      {
        success: false,
        error: '获取积分周期状态失败',
      },
      500
    )
  }
}

/**
 * 初始化用户积分周期
 */
export async function initializePointsCycleHandler(c: Context<AppContext>): Promise<Response> {
  try {
    const user = c.get('user')
    const requestData = await c.req.json()
    const { membershipLevel, startDate } = requestData

    const cycleManager = createPointsCycleManager(c.env)
    const startDateTime = startDate ? new Date(startDate) : new Date()

    await cycleManager.initializeUserPointsCycle(
      user.id,
      membershipLevel as 'FREE' | 'PRO' | 'ELITE' | 'ULTRA',
      startDateTime
    )

    // 获取初始化后的状态
    const cycleInfo = await cycleManager.getUserPointsCycle(user.id)

    return c.json({
      success: true,
      data: cycleInfo,
      message: `${membershipLevel}会员积分周期初始化成功`,
    })
  } catch (error) {
    console.error('初始化积分周期失败:', error)
    return c.json(
      {
        success: false,
        error: '初始化积分周期失败',
      },
      500
    )
  }
}

/**
 * 手动检查并重置过期积分
 */
export async function checkAndResetCycleHandler(c: Context<AppContext>): Promise<Response> {
  try {
    const user = c.get('user')
    const cycleManager = createPointsCycleManager(c.env)

    const result = await cycleManager.checkAndHandleExpiredPoints(user.id)

    return c.json({
      success: true,
      data: {
        wasExpired: result.wasExpired,
        newCycle: result.newCycle,
        message: result.wasExpired ? '积分周期已重置' : '积分周期正常',
      },
    })
  } catch (error) {
    console.error('检查重置积分周期失败:', error)
    return c.json(
      {
        success: false,
        error: '检查重置积分周期失败',
      },
      500
    )
  }
}

/**
 * 处理会员升级积分补差
 */
export async function handleMembershipUpgradeHandler(c: Context<AppContext>): Promise<Response> {
  try {
    const user = c.get('user')
    const requestData = await c.req.json()
    const { fromLevel, toLevel, remainingDays } = requestData

    const cycleManager = createPointsCycleManager(c.env)

    const result = await cycleManager.handleMembershipUpgrade(
      user.id,
      fromLevel as 'FREE' | 'PRO' | 'ELITE' | 'ULTRA',
      toLevel as 'FREE' | 'PRO' | 'ELITE' | 'ULTRA',
      remainingDays
    )

    // 获取升级后的状态
    const cycleInfo = await cycleManager.getUserPointsCycle(user.id)

    return c.json({
      success: true,
      data: {
        bonusPoints: result.bonusPoints,
        newCycle: cycleInfo,
      },
      message: `会员升级成功，补发${result.bonusPoints}积分`,
    })
  } catch (error) {
    console.error('处理会员升级失败:', error)
    return c.json(
      {
        success: false,
        error: '处理会员升级失败',
      },
      500
    )
  }
}

/**
 * 批量处理过期积分（管理员接口）
 */
export async function batchProcessExpiredPointsHandler(c: Context<AppContext>): Promise<Response> {
  try {
    // TODO: 添加管理员权限检查
    const user = c.get('user')

    const cycleManager = createPointsCycleManager(c.env)
    const result = await cycleManager.batchProcessExpiredPoints()

    return c.json({
      success: true,
      data: {
        processedCount: result.processedCount,
        errors: result.errors,
        executedBy: user.id,
        executedAt: new Date().toISOString(),
      },
      message: `批量处理完成，成功处理${result.processedCount}个用户`,
    })
  } catch (error) {
    console.error('批量处理过期积分失败:', error)
    return c.json(
      {
        success: false,
        error: '批量处理过期积分失败',
      },
      500
    )
  }
}

/**
 * 积分周期系统健康检查
 */
export async function getPointsCycleHealthHandler(c: Context<AppContext>): Promise<Response> {
  try {
    // 简单的健康检查，获取系统状态
    const healthData = {
      service: 'points-cycle',
      status: 'healthy',
      timestamp: new Date().toISOString(),
      features: {
        cycleManagement: true,
        automaticReset: true,
        upgradeHandling: true,
        batchProcessing: true,
      },
    }

    return c.json({
      success: true,
      data: healthData,
    })
  } catch (error) {
    console.error('积分周期健康检查失败:', error)
    return c.json(
      {
        success: false,
        error: '积分周期健康检查失败',
      },
      500
    )
  }
}
