import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import { authMiddleware } from '@/middleware/auth'

// 导入控制器
import {
  validateActivationCodeHand<PERSON>,
  useActivationCodeHandler,
  getUserActivationHistoryHandler,
  createMembershipCodeHandler,
  createPointsCodeHandler,
  createBatchCodesHandler,
  getActivationCodesHandler,
  getActivationCodeStatsHandler,
  disableActivationCodeHandler,
  enableActivationCodeHandler
} from '../controllers/activation-code.controller'

// ==================== 验证模式 ====================

// 使用激活码模式
const useActivationCodeSchema = z.object({
  code: z.string().min(1, '激活码不能为空')
})

// 创建会员激活码模式
const createMembershipCodeSchema = z.object({
  membershipPlanId: z.string().uuid('会员计划ID格式错误'),
  description: z.string().optional(),
  expiresAt: z.string().optional()
})

// 创建积分包激活码模式
const createPointsCodeSchema = z.object({
  pointsPackageId: z.string().uuid('积分包ID格式错误'),
  description: z.string().optional(),
  expiresAt: z.string().optional(),
  count: z.number().min(1).max(100).optional().default(1)
})

// 批量创建激活码模式
const createBatchCodesSchema = z.object({
  type: z.enum(['membership', 'points'], { required_error: '激活码类型必须指定' }),
  membershipPlanId: z.string().uuid().optional(),
  pointsPackageId: z.string().uuid().optional(),
  count: z.number().min(1).max(1000, '批量创建数量不能超过1000'),
  description: z.string().optional(),
  expiresAt: z.string().optional()
}).refine(
  (data) => {
    if (data.type === 'membership' && !data.membershipPlanId) {
      return false
    }
    if (data.type === 'points' && !data.pointsPackageId) {
      return false
    }
    return true
  },
  {
    message: '会员激活码需要membershipPlanId，积分包激活码需要pointsPackageId'
  }
)

// 分页查询模式
const paginationSchema = z.object({
  limit: z.string().optional().default('20'),
  offset: z.string().optional().default('0')
})

// 管理员查询激活码模式
const adminQuerySchema = z.object({
  type: z.enum(['membership', 'points']).optional(),
  isUsed: z.string().optional(),
  isActive: z.string().optional(),
  batchId: z.string().optional(),
  limit: z.string().optional().default('20'),
  offset: z.string().optional().default('0')
})

const route = new Hono()

// ==================== 用户端路由 ====================

/**
 * 验证激活码
 * GET /api/app/activation-code/validate/:code
 */
route.get('/validate/:code', authMiddleware, validateActivationCodeHandler)

/**
 * 使用激活码
 * POST /api/app/activation-code/use
 */
route.post('/use', authMiddleware, zValidator('json', useActivationCodeSchema), useActivationCodeHandler)

/**
 * 获取用户激活历史
 * GET /api/app/activation-code/history
 */
route.get('/history', authMiddleware, zValidator('query', paginationSchema), getUserActivationHistoryHandler)

// ==================== 管理员路由 ====================

/**
 * 创建会员激活码
 * POST /api/app/activation-code/admin/membership
 */
route.post('/admin/membership', authMiddleware, zValidator('json', createMembershipCodeSchema), createMembershipCodeHandler)

/**
 * 创建积分包激活码
 * POST /api/app/activation-code/admin/points
 */
route.post('/admin/points', authMiddleware, zValidator('json', createPointsCodeSchema), createPointsCodeHandler)

/**
 * 批量创建激活码
 * POST /api/app/activation-code/admin/batch
 */
route.post('/admin/batch', authMiddleware, zValidator('json', createBatchCodesSchema), createBatchCodesHandler)

/**
 * 获取激活码列表
 * GET /api/app/activation-code/admin/codes
 */
route.get('/admin/codes', authMiddleware, zValidator('query', adminQuerySchema), getActivationCodesHandler)

/**
 * 获取激活码统计
 * GET /api/app/activation-code/admin/stats
 */
route.get('/admin/stats', authMiddleware, getActivationCodeStatsHandler)

/**
 * 禁用激活码
 * PUT /api/app/activation-code/admin/codes/:codeId/disable
 */
route.put('/admin/codes/:codeId/disable', authMiddleware, disableActivationCodeHandler)

/**
 * 启用激活码
 * PUT /api/app/activation-code/admin/codes/:codeId/enable
 */
route.put('/admin/codes/:codeId/enable', authMiddleware, enableActivationCodeHandler)

export default route
