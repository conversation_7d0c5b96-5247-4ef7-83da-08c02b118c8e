import { Context } from 'hono'
import type { Env } from '@/types/env'
import { getUserBySupabaseId } from '@/modules/app/user/repositories/user.repository'
import {
  createMembershipActivationCode,
  createPointsActivationCode,
  createActivationCodesBatch,
  useActivationCode,
  getActivationCodes,
  getUserActivationHistory,
  getActivationCodeStats,
  disableActivationCode,
  enableActivationCode,
  validateActivationCode,
} from '@/lib/db/queries/activation-code'

// 辅助函数：从Supabase用户上下文解析本地用户ID
async function resolveLocalUserId(c: Context<{ Bindings: Env }>): Promise<string> {
  const supabaseUser = c.get('user')
  if (!supabaseUser) {
    throw new Error('用户未认证')
  }

  const dbUser = await getUserBySupabaseId(c.env, supabaseUser.id)
  if (!dbUser) {
    throw new Error('用户数据不存在')
  }

  return dbUser.id
}

// 检查管理员权限
async function checkAdminPermission(c: Context<{ Bindings: Env }>): Promise<boolean> {
  try {
    const supabaseUser = c.get('user')
    if (!supabaseUser) {
      return false
    }

    const userMetadata = supabaseUser.user_metadata || supabaseUser.raw_user_meta_data || {}
    const isAdmin = userMetadata.role === 'admin' || userMetadata.isAdmin === true

    if (isAdmin) {
      return true
    }

    // 备用检查：检查特定的管理员邮箱
    const adminEmails = [
      '<EMAIL>',
    ]

    if (adminEmails.includes(supabaseUser.email)) {
      return true
    }

    return false
  } catch (error) {
    console.error('检查管理员权限失败:', error)
    return false
  }
}

// ==================== 用户端 API ====================

/**
 * 验证激活码处理器
 * GET /api/app/activation-code/validate/:code
 */
export const validateActivationCodeHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const code = c.req.param('code')
    const validation = await validateActivationCode(c.env, code)

    if (!validation.valid) {
      return c.json(
        {
          success: false,
          message: validation.reason,
        },
        400
      )
    }

    const activationCode = validation.activationCode! as any

    // 返回激活码信息（不包含敏感信息）
    return c.json({
      success: true,
      data: {
        valid: true,
        type: activationCode.type,
        description: activationCode.description,
        membershipPlan: activationCode.membership_plan
          ? {
              name: activationCode.membership_plan.name,
              durationDays: activationCode.membership_plan.duration_days,
              pointsIncluded: activationCode.membership_plan.points_included
            }
          : null,
        pointsPackage: activationCode.points_package
          ? {
              name: activationCode.points_package.name,
              points: activationCode.points_package.points,
              bonusPoints: activationCode.points_package.bonus_points || 0
            }
          : null,
        expiresAt: activationCode.expires_at
      }
    })
  } catch (error) {
    console.error('验证激活码失败:', error)
    return c.json(
      {
        success: false,
        message: '验证激活码失败'
      },
      500
    )
  }
}

/**
 * 使用激活码处理器
 * POST /api/app/activation-code/use
 */
export const useActivationCodeHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const userId = await resolveLocalUserId(c)
    const { code } = await c.req.json()

    if (!code || typeof code !== 'string') {
      return c.json(
        {
          success: false,
          message: '激活码不能为空'
        },
        400
      )
    }

    const result = await useActivationCode(c.env, {
      code: code.trim().toUpperCase(),
      userId,
      ipAddress: c.req.header('CF-Connecting-IP') || c.req.header('X-Forwarded-For') || 'unknown',
      userAgent: c.req.header('User-Agent') || 'unknown'
    })

    return c.json({
      success: true,
      data: result,
      message: '激活码使用成功'
    })
  } catch (error) {
    console.error('使用激活码失败:', error)
    return c.json(
      {
        success: false,
        message: error instanceof Error ? error.message : '激活失败，请稍后重试'
      },
      500
    )
  }
}

/**
 * 获取用户激活历史处理器
 * GET /api/app/activation-code/history
 */
export const getUserActivationHistoryHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const userId = await resolveLocalUserId(c)
    const query = c.req.query()
    const limit = parseInt(query.limit || '20')
    const offset = parseInt(query.offset || '0')

    const history = await getUserActivationHistory(c.env, userId, { limit, offset })

    return c.json({
      success: true,
      data: history,
      pagination: {
        limit,
        offset,
        total: history.length
      }
    })
  } catch (error) {
    console.error('获取激活历史失败:', error)
    return c.json(
      {
        success: false,
        message: '获取激活历史失败'
      },
      500
    )
  }
}

// ==================== 管理员 API ====================

/**
 * 创建会员激活码处理器
 * POST /api/app/activation-code/admin/membership
 */
export const createMembershipCodeHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const isAdmin = await checkAdminPermission(c)
    if (!isAdmin) {
      return c.json(
        {
          success: false,
          message: '权限不足'
        },
        403
      )
    }

    const userId = await resolveLocalUserId(c)
    const params = await c.req.json()

    const activationCode = await createMembershipActivationCode(c.env, {
      membershipPlanId: params.membershipPlanId,
      description: params.description,
      expiresAt: params.expiresAt ? new Date(params.expiresAt) : undefined,
      createdBy: userId
    })

    return c.json({
      success: true,
      data: activationCode,
      message: '会员激活码创建成功'
    })
  } catch (error) {
    console.error('创建会员激活码失败:', error)
    return c.json(
      {
        success: false,
        message: '创建会员激活码失败'
      },
      500
    )
  }
}

/**
 * 创建积分包激活码处理器
 * POST /api/app/activation-code/admin/points
 */
export const createPointsCodeHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const isAdmin = await checkAdminPermission(c)
    if (!isAdmin) {
      return c.json(
        {
          success: false,
          message: '权限不足'
        },
        403
      )
    }

    const userId = await resolveLocalUserId(c)
    const params = await c.req.json()

    const count = params.count || 1
    const codes = []

    for (let i = 0; i < count; i++) {
      const activationCode = await createPointsActivationCode(c.env, {
        pointsPackageId: params.pointsPackageId,
        description: params.description,
        expiresAt: params.expiresAt ? new Date(params.expiresAt) : undefined,
        createdBy: userId
      })
      codes.push(activationCode)
    }

    return c.json({
      success: true,
      data: codes,
      message: `成功创建 ${count} 个积分包激活码`
    })
  } catch (error) {
    console.error('创建积分包激活码失败:', error)
    return c.json(
      {
        success: false,
        message: '创建积分包激活码失败'
      },
      500
    )
  }
}

/**
 * 批量创建激活码处理器
 * POST /api/app/activation-code/admin/batch
 */
export const createBatchCodesHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const isAdmin = await checkAdminPermission(c)
    if (!isAdmin) {
      return c.json(
        {
          success: false,
          message: '权限不足'
        },
        403
      )
    }

    const userId = await resolveLocalUserId(c)
    const params = await c.req.json()

    const codes = await createActivationCodesBatch(c.env, {
      type: params.type,
      membershipPlanId: params.membershipPlanId,
      pointsPackageId: params.pointsPackageId,
      count: params.count,
      description: params.description,
      expiresAt: params.expiresAt ? new Date(params.expiresAt) : undefined,
      createdBy: userId
    })

    return c.json({
      success: true,
      data: codes,
      message: `批量创建 ${codes.length} 个激活码成功`
    })
  } catch (error) {
    console.error('批量创建激活码失败:', error)
    return c.json(
      {
        success: false,
        message: '批量创建激活码失败'
      },
      500
    )
  }
}

/**
 * 获取激活码列表处理器
 * GET /api/app/activation-code/admin/codes
 */
export const getActivationCodesHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const isAdmin = await checkAdminPermission(c)
    if (!isAdmin) {
      return c.json(
        {
          success: false,
          message: '权限不足'
        },
        403
      )
    }

    const query = c.req.query()
    const params = {
      type: query.type as 'membership' | 'points' | undefined,
      isUsed: query.isUsed ? query.isUsed === 'true' : undefined,
      isActive: query.isActive ? query.isActive === 'true' : undefined,
      batchId: query.batchId,
      limit: parseInt(query.limit || '20'),
      offset: parseInt(query.offset || '0')
    }

    const codes = await getActivationCodes(c.env, params)

    return c.json({
      success: true,
      data: codes,
      pagination: {
        limit: params.limit,
        offset: params.offset,
        total: codes.length
      }
    })
  } catch (error) {
    console.error('获取激活码列表失败:', error)
    return c.json(
      {
        success: false,
        message: '获取激活码列表失败'
      },
      500
    )
  }
}

/**
 * 获取激活码统计处理器
 * GET /api/app/activation-code/admin/stats
 */
export const getActivationCodeStatsHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const isAdmin = await checkAdminPermission(c)
    if (!isAdmin) {
      return c.json(
        {
          success: false,
          message: '权限不足'
        },
        403
      )
    }

    const stats = await getActivationCodeStats(c.env)

    return c.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('获取激活码统计失败:', error)
    return c.json(
      {
        success: false,
        message: '获取激活码统计失败'
      },
      500
    )
  }
}

/**
 * 禁用激活码处理器
 * PUT /api/app/activation-code/admin/codes/:codeId/disable
 */
export const disableActivationCodeHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const isAdmin = await checkAdminPermission(c)
    if (!isAdmin) {
      return c.json(
        {
          success: false,
          message: '权限不足'
        },
        403
      )
    }

    const codeId = c.req.param('codeId')
    await disableActivationCode(c.env, codeId)

    return c.json({
      success: true,
      message: '激活码已禁用'
    })
  } catch (error) {
    console.error('禁用激活码失败:', error)
    return c.json(
      {
        success: false,
        message: '禁用激活码失败'
      },
      500
    )
  }
}

/**
 * 启用激活码处理器
 * PUT /api/app/activation-code/admin/codes/:codeId/enable
 */
export const enableActivationCodeHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const isAdmin = await checkAdminPermission(c)
    if (!isAdmin) {
      return c.json(
        {
          success: false,
          message: '权限不足'
        },
        403
      )
    }

    const codeId = c.req.param('codeId')
    await enableActivationCode(c.env, codeId)

    return c.json({
      success: true,
      message: '激活码已启用'
    })
  } catch (error) {
    console.error('启用激活码失败:', error)
    return c.json(
      {
        success: false,
        message: '启用激活码失败'
      },
      500
    )
  }
}
