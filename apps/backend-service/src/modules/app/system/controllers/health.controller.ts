import { Context } from 'hono'
import type { Env } from '@/types/env'
import { createSupabaseClient } from '@/lib/supabase'

/**
 * 基础健康检查处理器
 * GET /api/app/system/health
 */
export const getHealthHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    return c.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'pleasurehub-backend-service',
      module: 'system'
    })
  } catch (error) {
    console.error('基础健康检查失败:', error)
    return c.json(
      {
        status: 'error',
        timestamp: new Date().toISOString(),
        service: 'pleasurehub-backend-service',
        module: 'system',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      500
    )
  }
}

/**
 * 详细健康检查处理器（包括数据库连接）
 * GET /api/app/system/health/detailed
 */
export const getDetailedHealthHandler = async (c: Context<{ Bindings: Env }>) => {
  const checks = {
    service: 'ok',
    timestamp: new Date().toISOString(),
    module: 'system',
    supabase: 'unknown',
    database: 'unknown',
    environment: 'unknown'
  }

  try {
    // 检查环境变量
    const requiredEnvVars = ['SUPABASE_URL', 'SUPABASE_SERVICE_ROLE_KEY']
    const missingVars = requiredEnvVars.filter(varName => !c.env[varName as keyof Env])
    
    if (missingVars.length > 0) {
      checks.environment = `missing: ${missingVars.join(', ')}`
    } else {
      checks.environment = 'ok'
    }

    // 检查 Supabase 连接
    try {
      const supabase = createSupabaseClient(c.env)
      const { data, error } = await supabase.from('characters').select('count').limit(1)

      if (error) {
        checks.supabase = 'error'
        checks.database = error.message
      } else {
        checks.supabase = 'ok'
        checks.database = 'ok'
      }
    } catch (error) {
      checks.supabase = 'error'
      checks.database = error instanceof Error ? error.message : 'Unknown error'
    }

    const isHealthy = checks.supabase === 'ok' && checks.database === 'ok' && checks.environment === 'ok'

    return c.json(checks, isHealthy ? 200 : 503)
  } catch (error) {
    console.error('详细健康检查失败:', error)
    checks.service = 'error'
    return c.json(
      {
        ...checks,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      500
    )
  }
}

/**
 * 系统状态检查处理器
 * GET /api/app/system/health/status
 */
export const getSystemStatusHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const systemInfo = {
      service: 'pleasurehub-backend-service',
      module: 'system',
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime ? `${Math.floor(process.uptime())}s` : 'unknown',
      memory: process.memoryUsage ? {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024 * 100) / 100,
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024 * 100) / 100,
        unit: 'MB'
      } : 'unknown',
      environment: {
        nodeEnv: c.env.NODE_ENV || 'unknown',
        platform: process.platform || 'unknown'
      },
      features: {
        authentication: true,
        database: true,
        fileUpload: true,
        realTimeEvents: true
      }
    }

    return c.json({
      success: true,
      data: systemInfo
    })
  } catch (error) {
    console.error('系统状态检查失败:', error)
    return c.json(
      {
        success: false,
        error: '系统状态检查失败',
        timestamp: new Date().toISOString()
      },
      500
    )
  }
}
