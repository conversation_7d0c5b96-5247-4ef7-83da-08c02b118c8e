import { Context } from 'hono'
import type { Env } from '@/types/env'
import { uploadToR2, getR2ConfigFromEnv, deleteFromR2 } from '@/lib/utils/r2-upload'
import {
  getLatestAppVersion,
  checkForUpdates,
  logUpdateEvent,
  getAppVersions,
  createAppVersion,
  createUpdatePolicy,
  getUpdateStats
} from '@/lib/db/queries/app-update'
import { getSupabase } from '@/lib/db/queries/base'
import { handleSupabaseSingleResult, TABLE_NAMES } from '@/lib/db/supabase-types'

/**
 * 检查应用更新处理器
 * GET /api/app/system/app-update/check
 */
export const checkAppUpdateHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const query = c.req.query()
    const { platform, currentVersion, versionCode, channel = 'production', deviceId } = query
    const env = c.env

    if (!platform || !currentVersion) {
      return c.json(
        {
          success: false,
          message: '缺少必要参数: platform, currentVersion'
        },
        400
      )
    }

    // 记录检查更新的事件
    if (deviceId) {
      await logUpdateEvent(env, {
        deviceId,
        currentVersion,
        updateType: 'apk',
        updateStatus: 'started'
      })
    }

    // 检查APK更新
    const apkUpdate = await checkForUpdates(env, currentVersion, 'apk', channel)

    // 检查热更新
    const hotfixUpdate = await checkForUpdates(env, currentVersion, 'hotfix', channel)

    return c.json({
      success: true,
      data: {
        hasApkUpdate: apkUpdate.hasUpdate,
        hasHotfixUpdate: hotfixUpdate.hasUpdate,
        apkUpdate: apkUpdate.hasUpdate
          ? {
              version: apkUpdate.version,
              policy: apkUpdate.policy
            }
          : null,
        hotfixUpdate: hotfixUpdate.hasUpdate
          ? {
              version: hotfixUpdate.version,
              policy: hotfixUpdate.policy
            }
          : null,
        currentVersion,
        platform,
        channel
      }
    })
  } catch (error) {
    console.error('检查更新失败:', error)
    return c.json(
      {
        success: false,
        message: '检查更新失败'
      },
      500
    )
  }
}

/**
 * 获取最新版本信息处理器
 * GET /api/app/system/app-update/latest
 */
export const getLatestVersionHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const query = c.req.query()
    const { versionType = 'apk' } = query
    const env = c.env

    if (versionType !== 'apk' && versionType !== 'hotfix') {
      return c.json(
        {
          success: false,
          message: 'versionType 必须是 apk 或 hotfix'
        },
        400
      )
    }

    const latestVersion = await getLatestAppVersion(env, versionType)

    if (!latestVersion) {
      return c.json({
        success: true,
        data: null,
        message: `没有找到${versionType}版本`
      })
    }

    return c.json({
      success: true,
      data: latestVersion
    })
  } catch (error) {
    console.error('获取最新版本失败:', error)
    return c.json(
      {
        success: false,
        message: '获取最新版本失败'
      },
      500
    )
  }
}

/**
 * 获取版本列表处理器
 * GET /api/app/system/app-update/versions
 */
export const getVersionsHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const query = c.req.query()
    const { versionType, limit = '20', offset = '0' } = query
    const env = c.env

    const options: any = {
      limit: parseInt(limit),
      offset: parseInt(offset)
    }

    if (versionType && (versionType === 'apk' || versionType === 'hotfix')) {
      options.versionType = versionType
    }

    const versions = await getAppVersions(env, options)

    return c.json({
      success: true,
      data: versions,
      pagination: {
        limit: options.limit,
        offset: options.offset,
        total: versions.length
      }
    })
  } catch (error) {
    console.error('获取版本列表失败:', error)
    return c.json(
      {
        success: false,
        message: '获取版本列表失败'
      },
      500
    )
  }
}

/**
 * 记录更新事件处理器
 * POST /api/app/system/app-update/log
 */
export const logUpdateEventHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const body = await c.req.json()
    const { deviceId, currentVersion, updateType, updateStatus, errorMessage } = body
    const env = c.env

    if (!deviceId || !currentVersion || !updateType || !updateStatus) {
      return c.json(
        {
          success: false,
          message: '缺少必要参数: deviceId, currentVersion, updateType, updateStatus'
        },
        400
      )
    }

    await logUpdateEvent(env, {
      deviceId,
      currentVersion,
      updateType,
      updateStatus,
      errorMessage
    })

    return c.json({
      success: true,
      message: '更新事件记录成功'
    })
  } catch (error) {
    console.error('记录更新事件失败:', error)
    return c.json(
      {
        success: false,
        message: '记录更新事件失败'
      },
      500
    )
  }
}

/**
 * 获取更新统计处理器
 * GET /api/app/system/app-update/stats
 */
export const getUpdateStatsHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const query = c.req.query()
    const { versionId, updateType, dateFrom, dateTo } = query
    const env = c.env

    const options: any = {}
    if (versionId) options.versionId = versionId
    if (updateType && (updateType === 'apk' || updateType === 'hotfix')) {
      options.updateType = updateType
    }
    if (dateFrom) options.dateFrom = dateFrom
    if (dateTo) options.dateTo = dateTo

    const stats = await getUpdateStats(env, options)

    return c.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('获取更新统计失败:', error)
    return c.json(
      {
        success: false,
        message: '获取更新统计失败'
      },
      500
    )
  }
}

// ==================== 管理员功能 ====================

/**
 * 上传应用文件处理器 (APK 或热更新包)
 * POST /api/app/system/app-update/admin/upload
 */
export const uploadAppFileHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const env = c.env

    // 获取R2配置
    const r2Config = getR2ConfigFromEnv(env)
    if (!r2Config) {
      return c.json(
        {
          success: false,
          message: 'R2存储配置未找到，请检查环境变量配置'
        },
        500
      )
    }

    const formData = await c.req.formData()
    const file = formData.get('file') as File
    const versionName = formData.get('versionName') as string
    const versionCode = formData.get('versionCode') as string
    const versionType = formData.get('versionType') as string

    if (!file || !versionName || !versionCode || !versionType) {
      return c.json(
        {
          success: false,
          message: '缺少必要参数: file, versionName, versionCode, versionType'
        },
        400
      )
    }

    if (versionType !== 'apk' && versionType !== 'hotfix') {
      return c.json(
        {
          success: false,
          message: 'versionType 必须是 apk 或 hotfix'
        },
        400
      )
    }

    // 文件大小和类型验证
    const maxSize = versionType === 'apk' ? 200 * 1024 * 1024 : 50 * 1024 * 1024
    const allowedTypes =
      versionType === 'apk'
        ? ['application/vnd.android.package-archive', 'application/octet-stream']
        : ['application/zip', 'application/octet-stream']

    if (file.size > maxSize) {
      return c.json(
        {
          success: false,
          message: `文件过大，${versionType === 'apk' ? 'APK' : '热更新包'}最大允许${Math.round(
            maxSize / 1024 / 1024
          )}MB`
        },
        400
      )
    }

    // 上传到R2
    const uploadOptions = {
      fileName: `${versionType === 'apk' ? 'app' : 'bundle'}_${versionName}_${versionCode}.${
        versionType === 'apk' ? 'apk' : 'zip'
      }`,
      folder: versionType === 'apk' ? 'apk-updates' : 'hotfix-updates',
      maxSize,
      allowedTypes,
      makePublic: true
    }

    const uploadResult = await uploadToR2(env, file, uploadOptions)

    if (!uploadResult.success) {
      return c.json(
        {
          success: false,
          message: uploadResult.error || '文件上传失败'
        },
        500
      )
    }

    return c.json({
      success: true,
      data: {
        fileUrl: uploadResult.url,
        fileName: uploadOptions.fileName,
        versionName,
        versionCode,
        versionType
      },
      message: '文件上传成功'
    })
  } catch (error) {
    console.error('上传应用文件失败:', error)
    return c.json(
      {
        success: false,
        message: '上传应用文件失败'
      },
      500
    )
  }
}

/**
 * 创建新版本处理器
 * POST /api/app/system/app-update/admin/versions
 */
export const createVersionHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const versionData = await c.req.json()
    const env = c.env

    const newVersion = await createAppVersion(env, versionData)

    return c.json({
      success: true,
      data: newVersion,
      message: '版本创建成功'
    })
  } catch (error) {
    console.error('创建版本失败:', error)
    return c.json(
      {
        success: false,
        message: '创建版本失败'
      },
      500
    )
  }
}

/**
 * 删除应用版本处理器
 * DELETE /api/app/system/app-update/admin/versions/:versionId
 */
export const deleteVersionHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const env = c.env
    const versionId = c.req.param('versionId')

    // 获取版本信息
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.appVersion)
      .select('*')
      .eq('id', versionId)
      .single()

    const { data: version, error } = handleSupabaseSingleResult(result)
    if (error || !version) {
      return c.json(
        {
          success: false,
          message: '版本不存在'
        },
        404
      )
    }

    // 删除R2中的文件
    if (version.downloadUrl) {
      try {
        const r2Config = getR2ConfigFromEnv(env)
        if (r2Config) {
          const urlParts = version.downloadUrl.split('/')
          const fileName = urlParts[urlParts.length - 1]
          const folder = version.versionType === 'apk' ? 'apk-updates' : 'hotfix-updates'
          await deleteFromR2(env, `${folder}/${fileName}`)
        }
      } catch (deleteError) {
        console.warn('删除R2文件失败:', deleteError)
      }
    }

    // 删除数据库记录
    const deleteResult = await supabase
      .from(TABLE_NAMES.appVersion)
      .delete()
      .eq('id', versionId)

    if (deleteResult.error) {
      return c.json(
        {
          success: false,
          message: '删除版本失败'
        },
        500
      )
    }

    return c.json({
      success: true,
      message: '版本删除成功'
    })
  } catch (error) {
    console.error('删除版本失败:', error)
    return c.json(
      {
        success: false,
        message: '删除版本失败'
      },
      500
    )
  }
}

/**
 * 创建更新策略处理器
 * POST /api/app/system/app-update/admin/policies
 */
export const createUpdatePolicyHandler = async (c: Context<{ Bindings: Env }>) => {
  try {
    const policyData = await c.req.json()
    const env = c.env

    const newPolicy = await createUpdatePolicy(env, policyData)

    return c.json({
      success: true,
      data: newPolicy,
      message: '更新策略创建成功'
    })
  } catch (error) {
    console.error('创建更新策略失败:', error)
    return c.json(
      {
        success: false,
        message: '创建更新策略失败'
      },
      500
    )
  }
}
