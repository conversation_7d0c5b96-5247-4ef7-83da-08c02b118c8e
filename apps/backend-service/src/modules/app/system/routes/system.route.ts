import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import { authMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'

// 导入健康检查控制器
import {
  getHealthHandler,
  getDetailedHealthHandler,
  getSystemStatusHandler
} from '../controllers/health.controller'

// 导入应用更新控制器
import {
  checkAppUpdateHandler,
  getLatestVersionHandler,
  getVersionsHandler,
  logUpdateEventHandler,
  getUpdateStatsHandler,
  uploadAppFileHandler,
  createVersionHandler,
  deleteVersionHandler,
  createUpdatePolicyHandler
} from '../controllers/app-update.controller'

// ==================== 验证模式 ====================

// 检查更新查询参数验证
const checkUpdateQuerySchema = z.object({
  platform: z.string().min(1),
  currentVersion: z.string().min(1),
  versionCode: z.string().optional(),
  channel: z.enum(['production', 'beta', 'alpha']).optional().default('production'),
  deviceId: z.string().optional()
})

// 更新事件记录验证
const updateEventSchema = z.object({
  deviceId: z.string().min(1),
  currentVersion: z.string().min(1),
  updateType: z.enum(['apk', 'hotfix']),
  updateStatus: z.enum(['started', 'downloading', 'installing', 'completed', 'failed']),
  errorMessage: z.string().optional()
})

// 创建版本验证
const createVersionSchema = z.object({
  versionName: z.string().min(1),
  versionCode: z.number().int().positive(),
  versionType: z.enum(['apk', 'hotfix']),
  downloadUrl: z.string().url(),
  fileSize: z.number().int().positive(),
  releaseNotes: z.string().optional(),
  isActive: z.boolean().optional().default(true),
  channel: z.enum(['production', 'beta', 'alpha']).optional().default('production')
})

// 创建更新策略验证
const createPolicySchema = z.object({
  versionId: z.string().uuid(),
  channel: z.enum(['production', 'beta', 'alpha']).default('production'),
  updateType: z.enum(['optional', 'recommended', 'forced']).default('optional'),
  rolloutPercentage: z.number().min(0).max(100).default(100),
  minOsVersion: z.string().optional(),
  maxOsVersion: z.string().optional(),
  targetRegions: z.array(z.string()).optional(),
  isActive: z.boolean().default(true)
})

const route = new Hono()

// 应用中间件
route.use(languageMiddleware)

// ==================== 健康检查路由 ====================

// 基础健康检查（无需认证）
route.get('/health', getHealthHandler)

// 详细健康检查（无需认证）
route.get('/health/detailed', getDetailedHealthHandler)

// 系统状态检查（需要认证）
route.get('/health/status', authMiddleware, getSystemStatusHandler)

// ==================== 应用更新路由 ====================

// 检查更新（无需认证，供客户端使用）
route.get('/app-update/check', zValidator('query', checkUpdateQuerySchema), checkAppUpdateHandler)

// 获取最新版本（无需认证）
route.get('/app-update/latest', getLatestVersionHandler)

// 获取版本列表（需要认证）
route.get('/app-update/versions', authMiddleware, getVersionsHandler)

// 记录更新事件（无需认证，供客户端使用）
route.post('/app-update/log', zValidator('json', updateEventSchema), logUpdateEventHandler)

// 获取更新统计（需要认证）
route.get('/app-update/stats', authMiddleware, getUpdateStatsHandler)

// ==================== 管理员路由 ====================

// 上传应用文件（需要认证）
route.post('/app-update/admin/upload', authMiddleware, uploadAppFileHandler)

// 创建新版本（需要认证）
route.post('/app-update/admin/versions', authMiddleware, zValidator('json', createVersionSchema), createVersionHandler)

// 删除版本（需要认证）
route.delete('/app-update/admin/versions/:versionId', authMiddleware, deleteVersionHandler)

// 创建更新策略（需要认证）
route.post('/app-update/admin/policies', authMiddleware, zValidator('json', createPolicySchema), createUpdatePolicyHandler)

export default route
